import ConfirmationModal from 'src/components/_modals/Confirmation'

import { ctIntl } from 'cartrack-ui-kit'
import useForceTerminateBookingMutation from '../api/useForceTerminateBookingMutation'

type Props = {
  onClose: () => void
  onConfirm?: () => void
  bookingIds: Array<string | number>
}

function ForceTerminateBookingModal({ onClose, onConfirm, bookingIds }: Props) {
  const forceTerminateBookingMutation = useForceTerminateBookingMutation()

  const handleConfirm = () => {
    forceTerminateBookingMutation.mutate(
      {
        bookingIds: bookingIds as Array<string>,
      },
      {
        onSuccess() {
          onClose()
        },
      },
    )

    onConfirm?.()
  }

  return (
    <ConfirmationModal
      title="carpool.list.forceTerminateBooking"
      open
      confirmButtonLabel="carpool.list.forceTerminateBooking.buttonLabel"
      onClose={onClose}
      onConfirm={handleConfirm}
      isLoading={forceTerminateBookingMutation.isPending}
    >
      {ctIntl.formatMessage(
        { id: 'carpool.list.forceTerminateBooking.confirmMessage' },
        {
          values: {
            bookingNumber: bookingIds.join(', '),
          },
        },
      )}
    </ConfirmationModal>
  )
}

export default ForceTerminateBookingModal
