import { useMutation } from '@tanstack/react-query'

import { restPatch } from 'src/api/rest-api-caller'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'

import { ctIntl } from 'cartrack-ui-kit'
import useBookingMutationInvalidation from '../../../components/ScdfIssuanceRequestDrawer/api/useBookingMutationInvalidation'

export declare namespace ScdfForceTerminateBooking {
  type Params = {
    bookingIds: Array<string>
    reasonId: number
    remarks: string
    clientUserId: string
  }

  type RequestPayload = {
    bookingIds: Array<string>
    bookingForceTerminateReasonId: number
    bookingForceTerminateNotes: string
    forceTerminateClientUserId: string
  }

  type Response = {
    id: number
    message?: string
  }
}

function forceTerminateBooking(
  params: ScdfForceTerminateBooking.Params,
): Promise<ScdfForceTerminateBooking.Response> {
  const requestPayload: ScdfForceTerminateBooking.RequestPayload = {
    bookingIds: params.bookingIds,
    bookingForceTerminateReasonId: params.reasonId,
    bookingForceTerminateNotes: params.remarks || '',
    forceTerminateClientUserId: params.clientUserId,
  }

  return restPatch<ScdfForceTerminateBooking.Response>(
    `/scdf/booking/forceTerminate`,
    requestPayload,
  )
}

const useForceTerminateBookingMutation = () => {
  const invalidateQueries = useBookingMutationInvalidation()

  return useMutation({
    mutationFn: forceTerminateBooking,
    onSuccess: () => {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'carpool.list.forceTerminateBookingSuccessMessage',
        }),
        { variant: 'success' },
      )
      invalidateQueries({ shouldInvalidateSpecificBooking: false })
    },
    onError: () => {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'carpool.error.forceTerminateBookingGeneralMessage',
        }),
        { variant: 'error' },
      )
    },
  })
}

export default useForceTerminateBookingMutation
