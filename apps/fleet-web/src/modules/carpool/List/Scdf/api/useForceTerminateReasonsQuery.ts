import { useQuery } from '@tanstack/react-query'

import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import { restGet } from 'api/rest-api-caller'
import { createQuery } from 'src/util-functions/react-query-utils'

export declare namespace FetchForceTerminateReasons {
  type ForceTerminateReason = {
    id: number
    title: string
  }

  type OutPut = { data: Array<ForceTerminateReason> }
}

async function getForceTerminateReasons() {
  // Simulate API call with delay
  const result = await restGet<FetchForceTerminateReasons.OutPut>(
    '/booking/force-terminate-booking-reasons',
  )

  return result.data
}

export const forceTerminateReasonsQueryKey = () =>
  ['booking/forceTerminateReasons'] as const

const forceTerminateReasonsQuery = () =>
  createQuery({
    queryKey: forceTerminateReasonsQueryKey(),
    queryFn: getForceTerminateReasons,
    ...makeQueryErrorHandlerWithToast(),
  })

export const useForceTerminateReasonsQuery = () =>
  useQuery(forceTerminateReasonsQuery())
