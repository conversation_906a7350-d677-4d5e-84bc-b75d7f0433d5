import { useMemo } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { Autocomplete, Stack, TextField } from '@karoo-ui/core'
import { Controller, useForm } from 'react-hook-form'
import { z } from 'zod'

import { getAuthenticatedUser } from 'duxs/user'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import ConfirmationModal from 'src/components/_modals/Confirmation'
import { useTypedSelector } from 'src/redux-hooks'
import { messages } from 'src/shared/formik'
import IntlTypography from 'src/util-components/IntlTypography'

import { ctIntl } from 'cartrack-ui-kit'
import useForceTerminateBookingMutation from '../api/useForceTerminateBookingMutation'
import { useForceTerminateReasonsQuery } from '../api/useForceTerminateReasonsQuery'

const forceTerminateBookingSchema = z.object({
  reasonId: z
    .number()
    .nullable()
    .refine((val) => val !== null && val > 0, {
      message: messages.required,
    }),
  remarks: z.string().default(''),
})

type ForceTerminateBookingFormData = z.infer<typeof forceTerminateBookingSchema>

type ReasonOption = {
  id: number
  label: string
}

type ForceTerminateBookingModalProps = {
  onClose: () => void
  onConfirm?: () => void
  bookingIds: Array<string>
}

const ForceTerminateBookingModal = ({
  onClose,
  onConfirm,
  bookingIds,
}: ForceTerminateBookingModalProps) => {
  const { cuid: clientUserId } = useTypedSelector(getAuthenticatedUser)
  const reasonsQuery = useForceTerminateReasonsQuery()
  const forceTerminateBookingMutation = useForceTerminateBookingMutation()

  const {
    control,
    handleSubmit,
    reset,
    formState: { isValid },
  } = useForm<ForceTerminateBookingFormData>({
    resolver: zodResolver(forceTerminateBookingSchema),
    mode: 'onChange',
    defaultValues: { reasonId: null as any, remarks: '' },
  })

  const reasonOptions = useMemo(() => {
    const array: Array<ReasonOption> = []
    const byId = new Map<number, ReasonOption>()

    if (reasonsQuery.data) {
      for (const reason of reasonsQuery.data) {
        const option = { id: reason.id, label: reason.title }
        array.push(option)
        byId.set(option.id, option)
      }
    }

    return { array, byId }
  }, [reasonsQuery.data])

  const handleModalClose = () => {
    reset()
    onClose()
  }

  const handleFormSubmit = handleSubmit((data) => {
    if (data.reasonId === null) {
      return // This should not happen due to form validation
    }

    forceTerminateBookingMutation.mutate(
      {
        bookingIds,
        reasonId: data.reasonId,
        remarks: data.remarks,
        clientUserId: clientUserId ?? '',
      },
      {
        onSuccess() {
          onClose()
          onConfirm?.()
        },
      },
    )
  })

  return (
    <ConfirmationModal
      open={true}
      onClose={handleModalClose}
      onConfirm={handleFormSubmit}
      title={ctIntl.formatMessage({ id: 'tfms.list.forceTerminate' })}
      confirmButtonLabel="CONFIRM"
      confirmButtonVariant="contained"
      isLoading={forceTerminateBookingMutation.isPending}
      disabledConfirmButton={!isValid}
    >
      <Stack gap={2}>
        <IntlTypography
          color="text.secondary"
          msgProps={{
            id: 'tfms.forceTerminate.selectReason.description',
            values: { count: bookingIds.length, bookingIds: bookingIds.join(', ') },
          }}
        />

        <Controller
          control={control}
          name="reasonId"
          render={({ field, fieldState }) => (
            <Autocomplete<ReasonOption>
              size="small"
              loading={reasonsQuery.isPending}
              {...getAutocompleteVirtualizedProps({
                options: reasonOptions.array,
              })}
              onChange={(_, newValue) => {
                field.onChange(newValue ? newValue.id : null)
              }}
              value={field.value ? reasonOptions.byId.get(field.value) ?? null : null}
              renderInput={(params) => (
                <TextField
                  {...params}
                  required
                  label={ctIntl.formatMessage({
                    id: 'tfms.forceTerminate.selectReason.label',
                  })}
                  helperText={ctIntl.formatMessage({
                    id: fieldState.error?.message ?? '',
                  })}
                  error={!!fieldState.error}
                />
              )}
            />
          )}
        />

        <Controller
          control={control}
          name="remarks"
          render={({ field }) => (
            <TextField
              {...field}
              label={ctIntl.formatMessage({ id: 'Notes' })}
              multiline
              rows={3}
              variant="outlined"
              size="small"
              fullWidth
            />
          )}
        />
      </Stack>
    </ConfirmationModal>
  )
}

export default ForceTerminateBookingModal
